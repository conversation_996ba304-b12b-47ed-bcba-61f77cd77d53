import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

interface DashboardProps {
  setActiveTab?: (tab: string) => void;
}

export function Dashboard({ setActiveTab }: DashboardProps) {
  const user = useQuery(api.users.myProfile);
  const leads = useQuery(api.leads.listMine, {});
  const deals = useQuery(api.deals.listForPartner, {});
  const earnings = useQuery(api.earnings.overview, {});

  if (!user || leads === undefined || deals === undefined || earnings === undefined) {
    return (
      <div className="flex justify-center items-center py-grid-8">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          <p className="text-size-4 text-muted">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Debug logging to check earnings calculation
  console.log('🔍 Dashboard Debug Info:');
  console.log('📊 Deals:', deals);
  console.log('💰 Earnings:', earnings);
  console.log('👤 User tier:', user.tier);

  // Check each deal's commission values
  deals.forEach(deal => {
    const totalCommission = (deal.commissionDueTokenUsd || 0) + (deal.commissionDueFiatUsd || 0) + (deal.commissionPendingUsd || 0);
    console.log(`📋 Deal "${deal.projectName}":`, {
      status: deal.status,
      dealValue: deal.dealValueUsd,
      commissionDueToken: deal.commissionDueTokenUsd,
      commissionDueFiat: deal.commissionDueFiatUsd,
      commissionPending: deal.commissionPendingUsd,
      totalCommission,
      contributes: (deal.status === "closed" || deal.status === "paid") ? 'YES to pending' : 'NO'
    });
  });

  const getTierInfo = (tier: string | undefined) => {
    switch (tier) {
      case "elite":
        return {
          name: "Elite Partner",
          commission: "7.5%",
          icon: "💎",
          gradient: "from-purple-500/10 to-pink-500/10",
          border: "border-purple-200",
          badge: "bg-purple-100 text-purple-800"
        };
      case "diamond":
        return {
          name: "Diamond Partner",
          commission: "10%",
          icon: "👑",
          gradient: "from-yellow-500/10 to-orange-500/10",
          border: "border-yellow-200",
          badge: "bg-yellow-100 text-yellow-800"
        };
      default:
        return {
          name: "Trusted Partner",
          commission: "5%",
          icon: "🤝",
          gradient: "from-blue-500/10 to-cyan-500/10",
          border: "border-blue-200",
          badge: "bg-blue-100 text-blue-800"
        };
    }
  };

  const tierInfo = getTierInfo(user.tier);

  const stats = [
    {
      label: "Total Leads",
      value: leads.length,
      icon: "👥",
      color: "bg-blue-50 text-blue-700",
      trend: null,
    },
    {
      label: "Active Deals",
      value: deals.filter(d => d.status === "in_progress").length,
      icon: "💼",
      color: "bg-green-50 text-green-700",
      trend: null,
    },
    {
      label: "Closed Deals",
      value: deals.filter(d => d.status === "closed" || d.status === "paid").length,
      icon: "✅",
      color: "bg-purple-50 text-purple-700",
      trend: null,
    },
    {
      label: "Pending Earnings",
      value: `$${earnings.pending.toLocaleString()}`,
      icon: "💰",
      color: "bg-yellow-50 text-yellow-700",
      trend: null,
    },
  ];

  return (
    <div className="space-y-grid-8">
      {/* Header Section */}
      <div className="space-y-grid-2">
        <h2 className="text-size-1 text-foreground">Dashboard Overview</h2>
        <p className="text-size-3 text-muted">Welcome back! Here's your current status and performance.</p>
      </div>



      {/* Enhanced Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-grid-4 lg:gap-grid-6">
        {stats.map((stat, index) => (
          <div key={index} className="card group hover:shadow-lg transition-all duration-200">
            <div className="card-content space-y-grid-4">
              <div className="flex items-center justify-between">
                <div className={`inline-flex items-center px-grid-3 py-grid-1 rounded-full text-size-4 font-medium ${stat.color}`}>
                  <span className="mr-grid-1">{stat.icon}</span>
                  {stat.label}
                </div>
                {stat.trend && (
                  <span className="text-size-4 text-accent font-medium">
                    {stat.trend}
                  </span>
                )}
              </div>
              <div className="space-y-grid-1">
                <p className="text-size-1 font-semibold text-foreground group-hover:text-primary transition-colors">
                  {stat.value}
                </p>
                <p className="text-size-4 text-muted">
                  {stat.trend ? "vs last month" : "Current total"}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Recent Activity */}
      <div className="grid lg:grid-cols-2 gap-grid-6">
        {/* Recent Leads */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-size-2 text-foreground">Recent Leads</h3>
              <span className="text-size-4 text-muted">{leads.length} total</span>
            </div>
          </div>
          <div className="card-content">
            {leads.length === 0 ? (
              <div className="text-center py-grid-8 space-y-grid-4">
                <div className="w-12 h-12 bg-muted/20 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-xl">👥</span>
                </div>
                <div className="space-y-grid-2">
                  <p className="text-size-3 text-muted">No leads submitted yet</p>
                  <p className="text-size-4 text-muted">Start by submitting your first lead to begin earning commissions</p>
                </div>
              </div>
            ) : (
              <div className="space-y-grid-3">
                {leads.slice(0, 5).map((lead) => (
                  <div key={lead._id} className="flex justify-between items-center py-grid-3 border-b border-border last:border-b-0">
                    <div className="space-y-grid-1">
                      <p className="text-size-3 font-medium text-foreground">{lead.company}</p>
                      <p className="text-size-4 text-muted">{lead.status}</p>
                    </div>
                    <span className={`px-grid-2 py-grid-1 rounded-lg text-size-4 font-medium ${
                      lead.approved === true ? "bg-green-100 text-green-800" :
                      lead.approved === false ? "bg-destructive/10 text-destructive" :
                      "bg-accent/10 text-accent-foreground"
                    }`}>
                      {lead.approved === true ? "✅ Approved" :
                       lead.approved === false ? "❌ Rejected" : "⏳ Pending"}
                    </span>
                  </div>
                ))}
                {leads.length > 5 && (
                  <div className="pt-grid-2">
                    <p className="text-size-4 text-muted text-center">
                      +{leads.length - 5} more leads
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Recent Deals */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-size-2 text-foreground">Recent Deals</h3>
              <span className="text-size-4 text-muted">{deals.length} total</span>
            </div>
          </div>
          <div className="card-content">
            {deals.length === 0 ? (
              <div className="text-center py-grid-8 space-y-grid-4">
                <div className="w-12 h-12 bg-muted/20 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-xl">💼</span>
                </div>
                <div className="space-y-grid-2">
                  <p className="text-size-3 text-muted">No deals yet</p>
                  <p className="text-size-4 text-muted">Deals will appear here once your leads are approved and converted</p>
                </div>
              </div>
            ) : (
              <div className="space-y-grid-3">
                {deals.slice(0, 5).map((deal) => (
                  <div key={deal._id} className="flex justify-between items-center py-grid-3 border-b border-border last:border-b-0">
                    <div className="space-y-grid-1">
                      <p className="text-size-3 font-medium text-foreground">{deal.projectName}</p>
                      <p className="text-size-4 text-muted">
                        {deal.dealValueUsd ? `$${deal.dealValueUsd.toLocaleString()}` : "Value TBD"}
                      </p>
                    </div>
                    <span className={`px-grid-2 py-grid-1 rounded-lg text-size-4 font-medium ${
                      deal.status === "closed" || deal.status === "paid" ? "bg-green-100 text-green-800" :
                      deal.status === "in_progress" ? "bg-blue-100 text-blue-800" :
                      "bg-muted/20 text-muted"
                    }`}>
                      {deal.status === "closed" ? "✅ Closed" :
                       deal.status === "paid" ? "💰 Paid" :
                       deal.status === "in_progress" ? "🔄 In Progress" :
                       deal.status}
                    </span>
                  </div>
                ))}
                {deals.length > 5 && (
                  <div className="pt-grid-2">
                    <p className="text-size-4 text-muted text-center">
                      +{deals.length - 5} more deals
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Resources Section */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center gap-grid-3">
            <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <div>
              <h3 className="text-size-2 text-foreground">Partner Resources</h3>
              <p className="text-size-4 text-muted">Essential tools and guides for your success</p>
            </div>
          </div>
        </div>
        <div className="card-content">
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-grid-4">
            {/* Partner Guide */}
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                console.log('Opening partner guide');
                toast.info('Partner guide will be available soon');
              }}
              className="group block p-grid-6 border border-border rounded-xl hover:border-primary hover:bg-primary/5 transition-all duration-200"
            >
              <div className="space-y-grid-3">
                <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="space-y-grid-1">
                  <h4 className="text-size-3 font-semibold text-foreground group-hover:text-primary transition-colors">
                    Partner Guide
                  </h4>
                  <p className="text-size-4 text-muted">
                    Complete guide to maximizing your partnership success
                  </p>
                </div>
              </div>
            </a>

            {/* Sales Materials */}
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                console.log('Opening sales materials');
                toast.info('Sales materials will be available soon');
              }}
              className="group block p-grid-6 border border-border rounded-xl hover:border-primary hover:bg-primary/5 transition-all duration-200"
            >
              <div className="space-y-grid-3">
                <div className="w-12 h-12 bg-accent/10 rounded-xl flex items-center justify-center group-hover:bg-accent/20 transition-colors">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="space-y-grid-1">
                  <h4 className="text-size-3 font-semibold text-foreground group-hover:text-primary transition-colors">
                    Sales Materials
                  </h4>
                  <p className="text-size-4 text-muted">
                    Presentations, case studies, and pitch decks
                  </p>
                </div>
              </div>
            </a>

            {/* Support Center */}
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                console.log('Opening support center');
                toast.info('Support center will be available soon');
              }}
              className="group block p-grid-6 border border-border rounded-xl hover:border-primary hover:bg-primary/5 transition-all duration-200 sm:col-span-2 lg:col-span-1"
            >
              <div className="space-y-grid-3">
                <div className="w-12 h-12 bg-secondary/20 rounded-xl flex items-center justify-center group-hover:bg-secondary/30 transition-colors">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <div className="space-y-grid-1">
                  <h4 className="text-size-3 font-semibold text-foreground group-hover:text-primary transition-colors">
                    Support Center
                  </h4>
                  <p className="text-size-4 text-muted">
                    Get help and contact your account manager
                  </p>
                </div>
              </div>
            </a>
          </div>

          {/* Additional Resources */}
          <div className="mt-grid-6 pt-grid-6 border-t border-border">
            <div className="grid sm:grid-cols-2 gap-grid-4">
              <div className="space-y-grid-2">
                <h4 className="text-size-3 font-semibold text-foreground">Training & Webinars</h4>
                <ul className="space-y-grid-1 text-size-4 text-muted">
                  <li className="flex items-center gap-grid-2">
                    <span className="w-1.5 h-1.5 bg-accent rounded-full"></span>
                    Monthly partner training sessions
                  </li>
                  <li className="flex items-center gap-grid-2">
                    <span className="w-1.5 h-1.5 bg-accent rounded-full"></span>
                    Product update webinars
                  </li>
                  <li className="flex items-center gap-grid-2">
                    <span className="w-1.5 h-1.5 bg-accent rounded-full"></span>
                    Sales technique workshops
                  </li>
                </ul>
              </div>
              <div className="space-y-grid-2">
                <h4 className="text-size-3 font-semibold text-foreground">Quick Links</h4>
                <ul className="space-y-grid-1 text-size-4">
                  <li>
                    <a href="#" className="text-primary hover:text-primary/80 transition-colors">
                      Commission Structure Guide
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-primary hover:text-primary/80 transition-colors">
                      Lead Qualification Checklist
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-primary hover:text-primary/80 transition-colors">
                      Partner Agreement Terms
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
