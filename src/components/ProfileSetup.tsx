import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

export function ProfileSetup() {
  const user = useQuery(api.auth.loggedInUser);
  const completeProfile = useMutation(api.users.completeProfile);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    fullName: "",
    preferredCommunication: [] as string[],
    telegram: "",
    whatsapp: "",
    companyName: "",
    xProfile: "",
    companyType: "",
    companyTypeOther: "",
    roleTitle: "",
    roleTitleOther: "",
    preferredPaymentMethod: "",
    termsAccepted: false,
  });

  const companyTypes = [
    "Venture Capital (VC)",
    "Central Exchange (CEX)",
    "Decentralized Exchange (DEX)",
    "Launchpad",
    "Marketing Agency",
    "Incubator/Accelerator",
    "Market Making (MM)",
    "Development",
    "Technology",
    "Research",
    "Data and aggregation platform",
    "External BD",
    "Deal Flow Individual",
    "Angel Investor",
    "Others"
  ];

  const roleTitles = ["CEO", "CTO", "CMO", "BD", "Founder", "Co-Founder", "Others"];
  const communicationMethods = ["whatsapp", "email", "telegram"];

  // Auto-fill email when user data is available
  useEffect(() => {
    if (user?.email && !formData.email) {
      setFormData(prev => ({ ...prev, email: user.email || "" }));
    }
  }, [user?.email, formData.email]);

  const handleCommunicationChange = (method: string, checked: boolean) => {
    if (checked) {
      setFormData({ ...formData, preferredCommunication: [...formData.preferredCommunication, method] });
    } else {
      setFormData({ ...formData, preferredCommunication: formData.preferredCommunication.filter(m => m !== method) });
    }
  };

  const sanitizeInput = (input: string, maxLength: number) => {
    return input.replace(/[<>"'&]/g, '').substring(0, maxLength).trim();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.email || !formData.fullName || !formData.companyName || 
        !formData.companyType || !formData.roleTitle || 
        !formData.preferredPaymentMethod || !formData.termsAccepted) {
      toast.error("Please fill in all required fields and accept the terms");
      return;
    }

    if (formData.preferredCommunication.length === 0) {
      toast.error("Please select at least one communication method");
      return;
    }

    if (formData.companyType === "Others" && !formData.companyTypeOther) {
      toast.error("Please specify the company type");
      return;
    }

    if (formData.roleTitle === "Others" && !formData.roleTitleOther) {
      toast.error("Please specify your role title");
      return;
    }

    try {
      setIsSubmitting(true);
      
      // Sanitize inputs
      const sanitizedData = {
        email: sanitizeInput(formData.email, 100),
        fullName: sanitizeInput(formData.fullName, 100),
        preferredCommunication: formData.preferredCommunication as ("whatsapp" | "email" | "telegram")[],
        telegram: formData.telegram ? sanitizeInput(formData.telegram, 50) : undefined,
        whatsapp: formData.whatsapp ? sanitizeInput(formData.whatsapp, 20) : undefined,
        companyName: sanitizeInput(formData.companyName, 100),
        xProfile: formData.xProfile ? sanitizeInput(formData.xProfile, 100) : undefined,
        companyType: formData.companyType === "Others" ? sanitizeInput(formData.companyTypeOther, 100) : formData.companyType,
        roleTitle: formData.roleTitle === "Others" ? sanitizeInput(formData.roleTitleOther, 50) : formData.roleTitle,
        preferredPaymentMethod: formData.preferredPaymentMethod as "bank" | "usdt",
      };

      const result = await completeProfile(sanitizedData);
      toast.success(`Profile completed successfully! Your referral code: ${result.referralCode}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to complete profile");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto mt-8 p-8 bg-white rounded-lg shadow-sm border">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-semibold mb-2">Complete Your Partner Profile</h2>
        <p className="text-gray-600">This information is required to access the partner portal</p>
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>Required:</strong> You must complete this profile to access partner features. 
            Your referral link will be automatically generated based on your information.
          </p>
        </div>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Personal Information */}
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Personal Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                required
                maxLength={100}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
              <input
                type="text"
                value={formData.fullName}
                onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                required
                maxLength={100}
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Preferred Communication Methods * (Select at least one)</label>
            <div className="flex gap-4">
              {communicationMethods.map((method) => (
                <label key={method} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.preferredCommunication.includes(method)}
                    onChange={(e) => handleCommunicationChange(method, e.target.checked)}
                    className="mr-2"
                  />
                  <span className="capitalize">{method}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Telegram Handle</label>
              <input
                type="text"
                value={formData.telegram}
                onChange={(e) => setFormData({ ...formData, telegram: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="@username"
                maxLength={50}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">WhatsApp Number</label>
              <input
                type="text"
                value={formData.whatsapp}
                onChange={(e) => setFormData({ ...formData, whatsapp: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="+1234567890"
                maxLength={20}
              />
            </div>
          </div>
        </div>

        {/* Company Information */}
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Company Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Company Name *</label>
              <input
                type="text"
                value={formData.companyName}
                onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                required
                maxLength={100}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">X (Twitter) Profile</label>
              <input
                type="url"
                value={formData.xProfile}
                onChange={(e) => setFormData({ ...formData, xProfile: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="https://x.com/username"
                maxLength={100}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Company Type *</label>
              <select
                value={formData.companyType}
                onChange={(e) => setFormData({ ...formData, companyType: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                required
              >
                <option value="">Select company type</option>
                {companyTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            {formData.companyType === "Others" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Specify Company Type *</label>
                <input
                  type="text"
                  value={formData.companyTypeOther}
                  onChange={(e) => setFormData({ ...formData, companyTypeOther: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                  maxLength={100}
                />
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Your Role *</label>
              <select
                value={formData.roleTitle}
                onChange={(e) => setFormData({ ...formData, roleTitle: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                required
              >
                <option value="">Select your role</option>
                {roleTitles.map((role) => (
                  <option key={role} value={role}>{role}</option>
                ))}
              </select>
            </div>
            {formData.roleTitle === "Others" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Specify Your Role *</label>
                <input
                  type="text"
                  value={formData.roleTitleOther}
                  onChange={(e) => setFormData({ ...formData, roleTitleOther: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                  maxLength={50}
                />
              </div>
            )}
          </div>
        </div>

        {/* Payment Details */}
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Payment Details</h3>
          <div className="space-y-4">

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Preferred Payment Method *</label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="bank"
                    checked={formData.preferredPaymentMethod === "bank"}
                    onChange={(e) => setFormData({ ...formData, preferredPaymentMethod: e.target.value })}
                    className="mr-2"
                  />
                  Bank Transfer
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="usdt"
                    checked={formData.preferredPaymentMethod === "usdt"}
                    onChange={(e) => setFormData({ ...formData, preferredPaymentMethod: e.target.value })}
                    className="mr-2"
                  />
                  USDT
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Terms & Conditions */}
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Terms & Conditions</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <label className="flex items-start">
              <input
                type="checkbox"
                checked={formData.termsAccepted}
                onChange={(e) => setFormData({ ...formData, termsAccepted: e.target.checked })}
                className="mr-3 mt-1"
                required
              />
              <span className="text-sm text-gray-700">
                I have read and agree to the <a href="#" className="text-primary hover:underline">Terms & Conditions</a> and <a href="#" className="text-primary hover:underline">Privacy Policy</a>. I understand that my application will be reviewed and I will be notified of the approval status. *
              </span>
            </label>
          </div>
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-primary text-primary-foreground px-6 py-3 rounded-md hover:bg-primary-hover transition-colors disabled:opacity-50 font-medium text-lg"
        >
          {isSubmitting ? "Completing Profile..." : "Complete Partner Profile"}
        </button>
      </form>
    </div>
  );
}
