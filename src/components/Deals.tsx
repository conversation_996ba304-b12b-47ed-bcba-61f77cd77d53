import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";
import { toast } from "sonner";

export function Deals() {
  const user = useQuery(api.users.myProfile);
  const deals = useQuery(api.deals.listForPartner, {});
  const createDeal = useMutation(api.deals.create);
  const updateDeal = useMutation(api.deals.update);
  
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState({
    partnerId: "",
    projectName: "",
    dealType: "",
    status: "in_progress" as "in_progress" | "closed" | "lost" | "paid",
    dealValueUsd: "",
    commissionPct: "",
    totalTokens: "",
    receivedTokens: "",
    liquidatedTokens: "",
    liquidationUsd: "",
    commissionDueTokenUsd: "",
    commissionDueFiatUsd: "",
    commissionPendingUsd: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.partnerId || !formData.projectName || !formData.dealType) {
      toast.error("Partner, project name, and deal type are required");
      return;
    }

    try {
      await createDeal({
        partnerId: formData.partnerId as Id<"users">,
        projectName: formData.projectName,
        dealType: formData.dealType,
        status: formData.status,
        dealValueUsd: formData.dealValueUsd ? parseFloat(formData.dealValueUsd) : undefined,
        commissionPct: parseFloat(formData.commissionPct) || 5,
        totalTokens: formData.totalTokens ? parseFloat(formData.totalTokens) : undefined,
        receivedTokens: formData.receivedTokens ? parseFloat(formData.receivedTokens) : undefined,
        liquidatedTokens: formData.liquidatedTokens ? parseFloat(formData.liquidatedTokens) : undefined,
        liquidationUsd: formData.liquidationUsd ? parseFloat(formData.liquidationUsd) : undefined,
        commissionDueTokenUsd: formData.commissionDueTokenUsd ? parseFloat(formData.commissionDueTokenUsd) : undefined,
        commissionDueFiatUsd: formData.commissionDueFiatUsd ? parseFloat(formData.commissionDueFiatUsd) : undefined,
        commissionPendingUsd: formData.commissionPendingUsd ? parseFloat(formData.commissionPendingUsd) : undefined,
      });
      toast.success("Deal created successfully");
      setFormData({
        partnerId: "",
        projectName: "",
        dealType: "",
        status: "in_progress",
        dealValueUsd: "",
        commissionPct: "",
        totalTokens: "",
        receivedTokens: "",
        liquidatedTokens: "",
        liquidationUsd: "",
        commissionDueTokenUsd: "",
        commissionDueFiatUsd: "",
        commissionPendingUsd: "",
      });
      setIsCreating(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create deal");
    }
  };

  const handleStatusUpdate = async (dealId: Id<"deals">, status: "in_progress" | "closed" | "lost" | "paid") => {
    try {
      await updateDeal({ dealId, status });
      toast.success("Deal status updated");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update deal");
    }
  };

  if (!user || deals === undefined) {
    return <div>Loading...</div>;
  }

  const canCreateDeals = user.role && ["sales", "ops", "accounting", "admin", "superadmin"].includes(user.role);
  const canUpdateDeals = user.role && ["sales", "ops", "accounting", "admin", "superadmin"].includes(user.role);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Deals</h2>
        {canCreateDeals && (
          <button
            onClick={() => setIsCreating(!isCreating)}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary-hover transition-colors"
          >
            {isCreating ? "Cancel" : "Create New Deal"}
          </button>
        )}
      </div>

      {canCreateDeals && isCreating && (
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Create New Deal</h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Partner ID *
                </label>
                <input
                  type="text"
                  value={formData.partnerId}
                  onChange={(e) => setFormData({ ...formData, partnerId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Partner user ID"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Project Name *
                </label>
                <input
                  type="text"
                  value={formData.projectName}
                  onChange={(e) => setFormData({ ...formData, projectName: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Deal Type *
                </label>
                <input
                  type="text"
                  value={formData.dealType}
                  onChange={(e) => setFormData({ ...formData, dealType: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="e.g., Token Sale, Advisory, etc."
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="in_progress">In Progress</option>
                  <option value="closed">Closed</option>
                  <option value="lost">Lost</option>
                  <option value="paid">Paid</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Deal Value (USD)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.dealValueUsd}
                  onChange={(e) => setFormData({ ...formData, dealValueUsd: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Commission %
                </label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.commissionPct}
                  onChange={(e) => setFormData({ ...formData, commissionPct: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="5.0"
                />
              </div>
            </div>
            <button
              type="submit"
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary-hover transition-colors"
            >
              Create Deal
            </button>
          </form>
        </div>
      )}

      <div className="space-y-4">
        {deals.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No deals found.
          </div>
        ) : (
          deals.map((deal) => (
            <div key={deal._id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold">{deal.projectName}</h3>
                  <p className="text-sm text-gray-600">Type: {deal.dealType}</p>
                  <p className="text-sm text-gray-600">
                    Commission: {deal.commissionPct}%
                  </p>
                  {deal.dealValueUsd && (
                    <p className="text-sm text-gray-600">
                      Value: ${deal.dealValueUsd.toLocaleString()}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    deal.status === "paid" ? "bg-green-100 text-green-800" :
                    deal.status === "closed" ? "bg-blue-100 text-blue-800" :
                    deal.status === "in_progress" ? "bg-yellow-100 text-yellow-800" :
                    "bg-red-100 text-red-800"
                  }`}>
                    {deal.status.replace("_", " ")}
                  </span>
                  <p className="text-xs text-gray-500 mt-1">
                    Updated: {new Date(deal.lastUpdatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {(deal.totalTokens || deal.receivedTokens || deal.liquidatedTokens) && (
                <div className="mb-4 p-3 bg-gray-50 rounded">
                  <h4 className="text-sm font-medium mb-2">Token Details</h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    {deal.totalTokens && (
                      <div>
                        <span className="text-gray-600">Total: </span>
                        <span className="font-medium">{deal.totalTokens.toLocaleString()}</span>
                      </div>
                    )}
                    {deal.receivedTokens && (
                      <div>
                        <span className="text-gray-600">Received: </span>
                        <span className="font-medium">{deal.receivedTokens.toLocaleString()}</span>
                      </div>
                    )}
                    {deal.liquidatedTokens && (
                      <div>
                        <span className="text-gray-600">Liquidated: </span>
                        <span className="font-medium">{deal.liquidatedTokens.toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                  {deal.liquidationUsd && (
                    <p className="text-sm mt-2">
                      <span className="text-gray-600">Liquidation Value: </span>
                      <span className="font-medium">${deal.liquidationUsd.toLocaleString()}</span>
                    </p>
                  )}
                </div>
              )}

              {(deal.commissionDueTokenUsd || deal.commissionDueFiatUsd || deal.commissionPendingUsd) && (
                <div className="mb-4 p-3 bg-blue-50 rounded">
                  <h4 className="text-sm font-medium mb-2">Commission Details</h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    {deal.commissionDueTokenUsd && (
                      <div>
                        <span className="text-gray-600">Token Commission: </span>
                        <span className="font-medium">${deal.commissionDueTokenUsd.toLocaleString()}</span>
                      </div>
                    )}
                    {deal.commissionDueFiatUsd && (
                      <div>
                        <span className="text-gray-600">Fiat Commission: </span>
                        <span className="font-medium">${deal.commissionDueFiatUsd.toLocaleString()}</span>
                      </div>
                    )}
                    {deal.commissionPendingUsd && (
                      <div>
                        <span className="text-gray-600">Pending: </span>
                        <span className="font-medium">${deal.commissionPendingUsd.toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {canUpdateDeals && (
                <div className="flex gap-2">
                  <select
                    value={deal.status}
                    onChange={(e) => handleStatusUpdate(deal._id, e.target.value as any)}
                    className="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value="in_progress">In Progress</option>
                    <option value="closed">Closed</option>
                    <option value="lost">Lost</option>
                    <option value="paid">Paid</option>
                  </select>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}
