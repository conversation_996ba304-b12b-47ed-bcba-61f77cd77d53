import { useState } from "react";
import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";
import { StatusBadge } from "./StatusBadge";
import { ActionButtons } from "./ActionButtons";

interface Lead {
  _id: Id<"leads">;
  company: string;
  website?: string;
  twitter?: string;
  pocName?: string;
  pocRole?: string;
  notes?: string;
  status: "warm" | "cold" | "won" | "lost";
  approved?: boolean;
  partnerId: Id<"users">;
  _creationTime: number;
}

interface ApprovalForm {
  leadId: Id<"leads"> | null;
  telegramUrl: string;
  dealType: string;
  commissionPct: number | null;
}

interface LeadTableProps {
  leads: Lead[];
  loading?: boolean;
  onApproveLead?: (leadId: Id<"leads">, approved: boolean, details?: {
    telegramGroupUrl?: string;
    dealType?: string;
    commissionPct?: number;
  }) => void;
  showActions?: boolean;
  showApprovalForm?: boolean;
  emptyMessage?: string;
}

export function LeadTable({ 
  leads, 
  loading = false,
  onApproveLead,
  showActions = true,
  showApprovalForm = true,
  emptyMessage = "No leads found"
}: LeadTableProps) {
  const [approvalForm, setApprovalForm] = useState<ApprovalForm>({
    leadId: null,
    telegramUrl: "",
    dealType: "Standard Partnership",
    commissionPct: null,
  });

  const handleApproveLead = (leadId: Id<"leads">, approved: boolean) => {
    if (!onApproveLead) return;
    
    if (approved && approvalForm.leadId === leadId) {
      onApproveLead(leadId, approved, {
        telegramGroupUrl: approvalForm.telegramUrl,
        dealType: approvalForm.dealType,
        commissionPct: approvalForm.commissionPct || undefined,
      });
      
      setApprovalForm({
        leadId: null,
        telegramUrl: "",
        dealType: "Standard Partnership",
        commissionPct: null,
      });
    } else {
      onApproveLead(leadId, approved);
    }
  };

  const columns: Column<Lead>[] = [
    {
      key: "company",
      label: "Company",
      sortable: true,
      render: (lead) => (
        <div>
          <div className="font-medium">{lead.company}</div>
          {lead.website && (
            <div className="text-sm text-gray-600">{lead.website}</div>
          )}
        </div>
      )
    },
    {
      key: "pocName",
      label: "Contact",
      render: (lead) => (
        <div>
          {lead.pocName && <div className="font-medium">{lead.pocName}</div>}
          {lead.pocRole && (
            <div className="text-sm text-gray-600">{lead.pocRole}</div>
          )}
        </div>
      )
    },
    {
      key: "status",
      label: "Lead Status",
      render: (lead) => (
        <StatusBadge status={lead.status} />
      )
    },
    {
      key: "approved",
      label: "Approval",
      render: (lead) => (
        <StatusBadge 
          status={lead.approved === true ? "approved" : 
                  lead.approved === false ? "rejected" : "pending"} 
          variant="lead-approval"
        />
      )
    },
    {
      key: "_creationTime",
      label: "Submitted",
      sortable: true,
      render: (lead) => new Date(lead._creationTime).toLocaleDateString()
    }
  ];

  // Add expandable notes column if any lead has notes
  const hasNotes = leads.some(lead => lead.notes);
  if (hasNotes) {
    columns.splice(3, 0, {
      key: "notes",
      label: "Notes",
      render: (lead) => lead.notes ? (
        <div className="max-w-xs">
          <div className="text-sm text-gray-600 truncate" title={lead.notes}>
            {lead.notes}
          </div>
        </div>
      ) : "-"
    });
  }

  // Add actions column if showActions is true
  if (showActions && onApproveLead) {
    columns.push({
      key: "actions",
      label: "Actions",
      render: (lead) => {
        if (lead.approved !== undefined) return null;
        
        return (
          <div className="space-y-2">
            {showApprovalForm && approvalForm.leadId === lead._id && (
              <div className="bg-gray-50 p-3 rounded-lg space-y-3 min-w-64">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Telegram Group URL
                  </label>
                  <input
                    type="url"
                    value={approvalForm.telegramUrl}
                    onChange={(e) => setApprovalForm({ ...approvalForm, telegramUrl: e.target.value })}
                    placeholder="https://t.me/..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Deal Type
                    </label>
                    <input
                      type="text"
                      value={approvalForm.dealType}
                      onChange={(e) => setApprovalForm({ ...approvalForm, dealType: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Commission %
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={approvalForm.commissionPct || ""}
                      onChange={(e) => setApprovalForm({ 
                        ...approvalForm, 
                        commissionPct: e.target.value ? parseFloat(e.target.value) : null 
                      })}
                      placeholder="Auto-set"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                </div>
              </div>
            )}
            
            <ActionButtons
              onApprove={() => {
                if (showApprovalForm && approvalForm.leadId !== lead._id) {
                  setApprovalForm({ ...approvalForm, leadId: lead._id });
                } else {
                  handleApproveLead(lead._id, true);
                }
              }}
              onReject={() => handleApproveLead(lead._id, false)}
              approveText={
                showApprovalForm && approvalForm.leadId === lead._id 
                  ? "Confirm Approve & Create Deal" 
                  : "Approve"
              }
            />
            
            {showApprovalForm && approvalForm.leadId === lead._id && (
              <button
                onClick={() => setApprovalForm({ ...approvalForm, leadId: null })}
                className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
              >
                Cancel
              </button>
            )}
          </div>
        );
      }
    });
  }

  return (
    <DataTable
      data={leads}
      columns={columns}
      loading={loading}
      emptyMessage={emptyMessage}
    />
  );
}