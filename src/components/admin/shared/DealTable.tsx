import { useState } from "react";
import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";
import { StatusBadge } from "./StatusBadge";
import { ActionButtons } from "./ActionButtons";

interface Deal {
  _id: Id<"deals">;
  projectName: string;
  dealType: string;
  status: "in_progress" | "closed" | "lost" | "paid";
  dealValueUsd?: number;
  totalTokens?: number;
  receivedTokens?: number;
  liquidatedTokens?: number;
  liquidationUsd?: number;
  commissionPct: number;
  commissionDueTokenUsd?: number;
  commissionDueFiatUsd?: number;
  commissionPendingUsd?: number;
  partnerId: Id<"users">;
  _creationTime: number;
}

interface PartnerInfo {
  _id: Id<"users">;
  tier?: "trusted" | "elite" | "diamond";
  name?: string;
  email?: string;
}

interface DealFormData {
  dealValueUsd: string;
  totalTokens: string;
  receivedTokens: string;
  liquidatedTokens: string;
  liquidationUsd: string;
  commissionDueTokenUsd: string;
  commissionDueFiatUsd: string;
  commissionPendingUsd: string;
  status: "in_progress" | "closed" | "lost" | "paid";
  additionalFees: string;
}

interface DealTableProps {
  deals: Deal[];
  partners?: PartnerInfo[];
  loading?: boolean;
  onUpdateDeal?: (dealId: Id<"deals">, updates: Partial<Deal>) => void;
  showActions?: boolean;
  showFinancials?: boolean;
  emptyMessage?: string;
}

export function DealTable({ 
  deals, 
  partners = [],
  loading = false,
  onUpdateDeal,
  showActions = true,
  showFinancials = false,
  emptyMessage = "No deals found"
}: DealTableProps) {
  const [editingDeal, setEditingDeal] = useState<Id<"deals"> | null>(null);
  const [dealForm, setDealForm] = useState<DealFormData>({
    dealValueUsd: "",
    totalTokens: "",
    receivedTokens: "",
    liquidatedTokens: "",
    liquidationUsd: "",
    commissionDueTokenUsd: "",
    commissionDueFiatUsd: "",
    commissionPendingUsd: "",
    status: "in_progress",
    additionalFees: "",
  });

  // Commission rates based on partner tier
  const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
    switch (tier) {
      case "diamond": return 15; // 15%
      case "elite": return 12;   // 12%
      case "trusted": return 10; // 10%
      default: return 8;         // 8% for no tier
    }
  };

  // Get partner info for a deal
  const getPartnerForDeal = (deal: Deal): PartnerInfo | undefined => {
    return partners.find(p => p._id === deal.partnerId);
  };

  // Calculate commission based on deal value and partner tier
  const calculateCommission = (dealValue: number, partnerId: Id<"users">): number => {
    const partner = partners.find(p => p._id === partnerId);
    const rate = getCommissionRate(partner?.tier);
    return dealValue * (rate / 100);
  };

  const handleEditDeal = (deal: Deal) => {
    setEditingDeal(deal._id);
    setDealForm({
      dealValueUsd: deal.dealValueUsd?.toString() || "",
      totalTokens: deal.totalTokens?.toString() || "",
      receivedTokens: deal.receivedTokens?.toString() || "",
      liquidatedTokens: deal.liquidatedTokens?.toString() || "",
      liquidationUsd: deal.liquidationUsd?.toString() || "",
      commissionDueTokenUsd: deal.commissionDueTokenUsd?.toString() || "",
      commissionDueFiatUsd: deal.commissionDueFiatUsd?.toString() || "",
      commissionPendingUsd: deal.commissionPendingUsd?.toString() || "",
      status: deal.status,
      additionalFees: "0",
    });
  };

  const handleSaveDeal = () => {
    if (!editingDeal || !onUpdateDeal) return;
    
    const updates: any = {};
    
    // Update status
    updates.status = dealForm.status;
    
    // Update financial values
    if (dealForm.dealValueUsd) updates.dealValueUsd = parseFloat(dealForm.dealValueUsd);
    if (dealForm.totalTokens) updates.totalTokens = parseFloat(dealForm.totalTokens);
    if (dealForm.receivedTokens) updates.receivedTokens = parseFloat(dealForm.receivedTokens);
    if (dealForm.liquidatedTokens) updates.liquidatedTokens = parseFloat(dealForm.liquidatedTokens);
    if (dealForm.liquidationUsd) updates.liquidationUsd = parseFloat(dealForm.liquidationUsd);
    if (dealForm.commissionDueTokenUsd) updates.commissionDueTokenUsd = parseFloat(dealForm.commissionDueTokenUsd);
    if (dealForm.commissionDueFiatUsd) updates.commissionDueFiatUsd = parseFloat(dealForm.commissionDueFiatUsd);
    if (dealForm.commissionPendingUsd) updates.commissionPendingUsd = parseFloat(dealForm.commissionPendingUsd);
    
    // Add additional fees to existing commission values
    if (dealForm.additionalFees && parseFloat(dealForm.additionalFees) > 0) {
      const additionalAmount = parseFloat(dealForm.additionalFees);
      updates.commissionDueFiatUsd = (updates.commissionDueFiatUsd || 0) + additionalAmount;
    }

    onUpdateDeal(editingDeal, updates);
    setEditingDeal(null);
  };

  const formatCurrency = (amount?: number) => {
    return amount ? `$${amount.toLocaleString()}` : "Not set";
  };

  const formatNumber = (num?: number) => {
    return num ? num.toLocaleString() : "Not set";
  };

  const columns: Column<Deal>[] = [
    {
      key: "projectName",
      label: "Project",
      sortable: true,
      render: (deal) => (
        <div>
          <div className="font-medium">{deal.projectName}</div>
          <div className="text-sm text-gray-600">
            {deal.dealType} • {deal.commissionPct}% commission
          </div>
        </div>
      )
    },
    {
      key: "status",
      label: "Status",
      render: (deal) => (
        <StatusBadge status={deal.status} variant="deal-status" />
      )
    },
    {
      key: "dealValueUsd",
      label: "Deal Value",
      sortable: true,
      render: (deal) => formatCurrency(deal.dealValueUsd)
    },
    {
      key: "_creationTime",
      label: "Created",
      sortable: true,
      render: (deal) => new Date(deal._creationTime).toLocaleDateString()
    }
  ];

  // Add financial columns if showFinancials is true
  if (showFinancials) {
    const financialColumns: Column<Deal>[] = [
      {
        key: "totalTokens",
        label: "Total Tokens",
        render: (deal) => formatNumber(deal.totalTokens)
      },
      {
        key: "liquidationUsd",
        label: "Liquidation",
        render: (deal) => formatCurrency(deal.liquidationUsd)
      },
      {
        key: "commissionTotal",
        label: "Total Commission",
        render: (deal) => {
          const total = (deal.commissionDueTokenUsd || 0) + 
                       (deal.commissionDueFiatUsd || 0) + 
                       (deal.commissionPendingUsd || 0);
          return formatCurrency(total);
        }
      }
    ];
    
    // Insert financial columns before the created column
    columns.splice(-1, 0, ...financialColumns);
  }

  // Add actions column if showActions is true
  if (showActions && onUpdateDeal) {
    columns.push({
      key: "actions",
      label: "Actions",
      render: (deal) => (
        <div className="space-y-2">
          {editingDeal === deal._id ? (
            <div className="bg-gray-50 p-4 rounded-lg space-y-4 min-w-96">
              {/* Status and Additional Fees Row */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Deal Status
                  </label>
                  <select
                    value={dealForm.status}
                    onChange={(e) => setDealForm({ ...dealForm, status: e.target.value as "in_progress" | "closed" | "lost" | "paid" })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="in_progress">In Progress</option>
                    <option value="closed">Closed</option>
                    <option value="lost">Lost</option>
                    <option value="paid">Paid</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Additional Fees (USD)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={dealForm.additionalFees}
                    onChange={(e) => setDealForm({ ...dealForm, additionalFees: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="0.00"
                  />
                </div>
              </div>

              {/* Partner Tier Info */}
              {(() => {
                const currentDeal = deals.find(d => d._id === editingDeal);
                const partner = currentDeal ? getPartnerForDeal(currentDeal) : undefined;
                const commissionRate = getCommissionRate(partner?.tier);
                
                return (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="font-medium text-blue-900">
                        Partner: {partner?.name || partner?.email || 'Unknown'}
                      </span>
                      <span className="text-blue-700">
                        Tier: {partner?.tier || 'None'} ({commissionRate}% commission)
                      </span>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      Commission is auto-calculated when Deal Value changes, but can be manually overridden
                    </p>
                  </div>
                );
              })()}
              
              {/* Financial Values Grid */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Deal Value (USD)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={dealForm.dealValueUsd}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      const currentDeal = deals.find(d => d._id === editingDeal);
                      
                      // Auto-calculate commission based on partner tier
                      if (newValue && currentDeal && parseFloat(newValue) > 0) {
                        const commission = calculateCommission(parseFloat(newValue), currentDeal.partnerId);
                        setDealForm({ 
                          ...dealForm, 
                          dealValueUsd: newValue,
                          commissionDueFiatUsd: commission.toString()
                        });
                      } else {
                        setDealForm({ ...dealForm, dealValueUsd: newValue });
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Total Tokens
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={dealForm.totalTokens}
                    onChange={(e) => setDealForm({ ...dealForm, totalTokens: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Commission Due (Token USD)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={dealForm.commissionDueTokenUsd}
                    onChange={(e) => setDealForm({ ...dealForm, commissionDueTokenUsd: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Commission Due (Fiat USD)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={dealForm.commissionDueFiatUsd}
                    onChange={(e) => setDealForm({ ...dealForm, commissionDueFiatUsd: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="0.00"
                  />
                </div>
              </div>
              <ActionButtons
                onApprove={handleSaveDeal}
                onReject={() => setEditingDeal(null)}
                approveText="Save Changes"
                rejectText="Cancel"
              />
            </div>
          ) : (
            <ActionButtons
              onEdit={() => handleEditDeal(deal)}
              editText="Edit Values"
            />
          )}
        </div>
      )
    });
  }

  return (
    <DataTable
      data={deals}
      columns={columns}
      loading={loading}
      emptyMessage={emptyMessage}
    />
  );
}