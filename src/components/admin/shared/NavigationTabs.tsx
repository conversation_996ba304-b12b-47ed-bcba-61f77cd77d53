interface Tab {
  id: string;
  label: string;
  count?: number;
}

interface NavigationTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export function NavigationTabs({ tabs, activeTab, onTabChange }: NavigationTabsProps) {
  return (
    <nav className="flex space-x-1 bg-gray-100 rounded-lg p-1">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2 ${
            activeTab === tab.id
              ? "bg-white text-blue-700 shadow-sm"
              : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
          }`}
        >
          {tab.label}
          {tab.count !== undefined && (
            <span className={`px-2 py-1 rounded-full text-xs ${
              activeTab === tab.id
                ? "bg-blue-100 text-blue-700"
                : "bg-gray-300 text-gray-700"
            }`}>
              {tab.count}
            </span>
          )}
        </button>
      ))}
    </nav>
  );
}