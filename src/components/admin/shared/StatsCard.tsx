interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  color?: "blue" | "green" | "yellow" | "purple" | "red";
  loading?: boolean;
}

const colorClasses = {
  blue: "bg-blue-50 border-blue-200 text-blue-900",
  green: "bg-green-50 border-green-200 text-green-900",
  yellow: "bg-yellow-50 border-yellow-200 text-yellow-900",
  purple: "bg-purple-50 border-purple-200 text-purple-900",
  red: "bg-red-50 border-red-200 text-red-900",
};

const valueColorClasses = {
  blue: "text-blue-700",
  green: "text-green-700",
  yellow: "text-yellow-700",
  purple: "text-purple-700",
  red: "text-red-700",
};

const descriptionColorClasses = {
  blue: "text-blue-600",
  green: "text-green-600",
  yellow: "text-yellow-600",
  purple: "text-purple-600",
  red: "text-red-600",
};

export function StatsCard({ 
  title, 
  value, 
  description, 
  color = "blue", 
  loading = false 
}: StatsCardProps) {
  if (loading) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 animate-pulse">
        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
        <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
        <div className="h-3 bg-gray-300 rounded w-full"></div>
      </div>
    );
  }

  const formattedValue = typeof value === 'number' ? value.toLocaleString() : value;

  return (
    <div className={`${colorClasses[color]} border rounded-lg p-6`}>
      <h3 className="font-semibold mb-2">{title}</h3>
      <p className={`text-2xl font-bold ${valueColorClasses[color]} mb-1`}>
        {formattedValue}
      </p>
      {description && (
        <p className={`text-sm ${descriptionColorClasses[color]}`}>
          {description}
        </p>
      )}
    </div>
  );
}