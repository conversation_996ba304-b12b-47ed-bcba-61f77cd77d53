import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";

export function Earnings() {
  const user = useQuery(api.users.myProfile);
  const earnings = useQuery(api.earnings.overview);
  const withdrawals = useQuery(api.earnings.listWithdrawals, {});
  const requestWithdrawal = useMutation(api.earnings.requestWithdrawal);
  
  const [isRequesting, setIsRequesting] = useState(false);
  const [formData, setFormData] = useState({
    amountUsd: "",
    method: "usdt" as "usdt" | "bank",
    walletAddress: "",
    bankDetails: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseFloat(formData.amountUsd);
    
    if (!amount || amount < 100) {
      toast.error("Minimum withdrawal amount is $100");
      return;
    }

    if (formData.method === "usdt" && !formData.walletAddress) {
      toast.error("Wallet address is required for USDT withdrawals");
      return;
    }

    if (formData.method === "bank" && !formData.bankDetails) {
      toast.error("Bank details are required for bank withdrawals");
      return;
    }

    try {
      await requestWithdrawal({
        amountUsd: amount,
        method: formData.method,
        walletAddress: formData.walletAddress || undefined,
        bankDetails: formData.bankDetails || undefined,
      });
      toast.success("Withdrawal request submitted successfully");
      setFormData({
        amountUsd: "",
        method: "usdt",
        walletAddress: "",
        bankDetails: "",
      });
      setIsRequesting(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to request withdrawal");
    }
  };

  if (!user || earnings === undefined || withdrawals === undefined) {
    return <div>Loading...</div>;
  }

  const canRequestWithdrawal = user.role === "partner";

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Earnings</h2>
        {canRequestWithdrawal && earnings.pending > 0 && (
          <button
            onClick={() => setIsRequesting(!isRequesting)}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary-hover transition-colors"
          >
            {isRequesting ? "Cancel" : "Request Withdrawal"}
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-blue-50 p-6 rounded-lg">
          <h3 className="text-sm font-medium text-blue-600 mb-2">Estimated Earnings</h3>
          <p className="text-3xl font-bold text-blue-900">
            ${earnings.estimated.toLocaleString()}
          </p>
        </div>
        
        <div className="bg-green-50 p-6 rounded-lg">
          <h3 className="text-sm font-medium text-green-600 mb-2">Withdrawn</h3>
          <p className="text-3xl font-bold text-green-900">
            ${earnings.withdrawn.toLocaleString()}
          </p>
        </div>
        
        <div className="bg-orange-50 p-6 rounded-lg">
          <h3 className="text-sm font-medium text-orange-600 mb-2">Available</h3>
          <p className="text-3xl font-bold text-orange-900">
            ${earnings.pending.toLocaleString()}
          </p>
        </div>
      </div>

      {canRequestWithdrawal && isRequesting && (
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Request Withdrawal</h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount (USD) *
              </label>
              <input
                type="number"
                min="100"
                step="0.01"
                value={formData.amountUsd}
                onChange={(e) => setFormData({ ...formData, amountUsd: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="100.00"
                required
              />
              <p className="text-xs text-gray-500 mt-1">Minimum: $100</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Method *
              </label>
              <select
                value={formData.method}
                onChange={(e) => setFormData({ ...formData, method: e.target.value as "usdt" | "bank" })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="usdt">USDT</option>
                <option value="bank">Bank Transfer</option>
              </select>
            </div>

            {formData.method === "usdt" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  USDT Wallet Address *
                </label>
                <input
                  type="text"
                  value={formData.walletAddress}
                  onChange={(e) => setFormData({ ...formData, walletAddress: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="0x..."
                  required
                />
              </div>
            )}

            {formData.method === "bank" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bank Details *
                </label>
                <textarea
                  value={formData.bankDetails}
                  onChange={(e) => setFormData({ ...formData, bankDetails: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Bank name, account number, routing number, etc."
                  required
                />
              </div>
            )}

            <button
              type="submit"
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary-hover transition-colors"
            >
              Submit Request
            </button>
          </form>
        </div>
      )}

      <div>
        <h3 className="text-lg font-semibold mb-4">Withdrawal History</h3>
        <div className="space-y-4">
          {withdrawals.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No withdrawal requests yet.
            </div>
          ) : (
            withdrawals.map((withdrawal) => (
              <div key={withdrawal._id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <p className="font-semibold">${withdrawal.amountUsd.toLocaleString()}</p>
                    <p className="text-sm text-gray-600">
                      Method: {withdrawal.method.toUpperCase()}
                    </p>
                    <p className="text-sm text-gray-600">
                      Requested: {new Date(withdrawal._creationTime).toLocaleDateString()}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    withdrawal.status === "paid" ? "bg-green-100 text-green-800" :
                    withdrawal.status === "approved" ? "bg-blue-100 text-blue-800" :
                    withdrawal.status === "in_review" ? "bg-yellow-100 text-yellow-800" :
                    "bg-red-100 text-red-800"
                  }`}>
                    {withdrawal.status.replace("_", " ")}
                  </span>
                </div>
                
                {withdrawal.txIdOrRef && (
                  <p className="text-sm text-gray-600">
                    Transaction: {withdrawal.txIdOrRef}
                  </p>
                )}
                
                {withdrawal.method === "usdt" && withdrawal.walletAddress && (
                  <p className="text-sm text-gray-600 font-mono">
                    Wallet: {withdrawal.walletAddress}
                  </p>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
