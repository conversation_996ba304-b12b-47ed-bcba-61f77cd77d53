import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";
import { toast } from "sonner";

export function Leads() {
  const user = useQuery(api.users.myProfile);
  const leads = useQuery(api.leads.listMine, {});
  const submitLead = useMutation(api.leads.submit);
  const approveLead = useMutation(api.leads.approve);
  const updateStatus = useMutation(api.leads.updateStatus);
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    company: "",
    website: "",
    twitter: "",
    pocName: "",
    pocRole: "",
    notes: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.company) {
      toast.error("Company name is required");
      return;
    }

    try {
      setIsSubmitting(true);
      await submitLead(formData);
      toast.success("Lead submitted successfully");
      setFormData({
        company: "",
        website: "",
        twitter: "",
        pocName: "",
        pocRole: "",
        notes: "",
      });
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to submit lead");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleApprove = async (leadId: Id<"leads">, approved: boolean, telegramGroupUrl?: string) => {
    try {
      await approveLead({ leadId, approved, telegramGroupUrl });
      toast.success(`Lead ${approved ? "approved" : "rejected"}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update lead");
    }
  };

  const handleStatusUpdate = async (leadId: Id<"leads">, status: "warm" | "cold" | "won" | "lost") => {
    try {
      await updateStatus({ leadId, status });
      toast.success("Lead status updated");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update status");
    }
  };

  if (!user || leads === undefined) {
    return <div>Loading...</div>;
  }

  const canSubmitLeads = user.role === "partner";
  const canApproveLeads = user.role ? ["ops", "admin", "superadmin"].includes(user.role) : false;
  const canUpdateStatus = user.role ? ["sales", "ops", "admin", "superadmin"].includes(user.role) : false;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Leads</h2>
        {canSubmitLeads && (
          <button
            onClick={() => setIsSubmitting(!isSubmitting)}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary-hover transition-colors"
          >
            {isSubmitting ? "Cancel" : "Submit New Lead"}
          </button>
        )}
      </div>

      {canSubmitLeads && (
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Lead Submission Options</h3>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• <strong>Manual Submission:</strong> Use the form below to submit leads directly</p>
            <p>• <strong>Referral Links:</strong> Share your referral links so prospects can submit their own information</p>
            <p>• When someone uses your referral link, their lead will automatically be assigned to you</p>
          </div>
        </div>
      )}

      {canSubmitLeads && isSubmitting && (
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Submit New Lead</h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Name *
                </label>
                <input
                  type="text"
                  value={formData.company}
                  onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Website
                </label>
                <input
                  type="url"
                  value={formData.website}
                  onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Twitter/X
                </label>
                <input
                  type="text"
                  value={formData.twitter}
                  onChange={(e) => setFormData({ ...formData, twitter: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  POC Name
                </label>
                <input
                  type="text"
                  value={formData.pocName}
                  onChange={(e) => setFormData({ ...formData, pocName: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  POC Role
                </label>
                <input
                  type="text"
                  value={formData.pocRole}
                  onChange={(e) => setFormData({ ...formData, pocRole: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary-hover transition-colors disabled:opacity-50"
            >
              Submit Lead
            </button>
          </form>
        </div>
      )}

      <div className="space-y-4">
        {leads.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No leads found. {canSubmitLeads && "Submit your first lead above or share your referral links!"}
          </div>
        ) : (
          leads.map((lead) => (
            <div key={lead._id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold">{lead.company}</h3>
                  <div className="text-sm text-gray-600 space-y-1">
                    {lead.website && <p>Website: {lead.website}</p>}
                    {lead.twitter && <p>Twitter: {lead.twitter}</p>}
                    {lead.pocName && <p>POC: {lead.pocName} {lead.pocRole && `(${lead.pocRole})`}</p>}
                    {lead.referralLinkId && (
                      <p className="text-blue-600">
                        <span className="inline-block w-2 h-2 bg-blue-600 rounded-full mr-1"></span>
                        From referral link
                      </p>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    lead.status === "won" ? "bg-green-100 text-green-800" :
                    lead.status === "warm" ? "bg-yellow-100 text-yellow-800" :
                    lead.status === "cold" ? "bg-blue-100 text-blue-800" :
                    "bg-red-100 text-red-800"
                  }`}>
                    {lead.status}
                  </span>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(lead._creationTime).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {lead.notes && (
                <div className="mb-4">
                  <p className="text-sm text-gray-700">{lead.notes}</p>
                </div>
              )}

              {lead.telegramGroupUrl && (
                <div className="mb-4">
                  <a
                    href={lead.telegramGroupUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Join Telegram Group →
                  </a>
                </div>
              )}

              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  {canUpdateStatus && (
                    <select
                      value={lead.status}
                      onChange={(e) => handleStatusUpdate(lead._id, e.target.value as any)}
                      className="text-sm border border-gray-300 rounded px-2 py-1"
                    >
                      <option value="warm">Warm</option>
                      <option value="cold">Cold</option>
                      <option value="won">Won</option>
                      <option value="lost">Lost</option>
                    </select>
                  )}
                </div>

                {canApproveLeads && lead.approved === undefined && (
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleApprove(lead._id, false)}
                      className="bg-red-100 text-red-700 px-3 py-1 rounded text-sm hover:bg-red-200 transition-colors"
                    >
                      Reject
                    </button>
                    <button
                      onClick={() => {
                        const telegramUrl = prompt("Telegram group URL (optional):");
                        handleApprove(lead._id, true, telegramUrl || undefined);
                      }}
                      className="bg-green-100 text-green-700 px-3 py-1 rounded text-sm hover:bg-green-200 transition-colors"
                    >
                      Approve
                    </button>
                  </div>
                )}

                {lead.approved !== undefined && (
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    lead.approved ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                  }`}>
                    {lead.approved ? "Approved" : "Rejected"}
                  </span>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
