@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design System Color Palette (Dark Mode Only) */
:root {
  /* Palette — docs/PALETTE.md */
  --background: #004643;        /* Background (60%) */
  --foreground: #fffffe;        /* Headline (30%) */
  --muted: #abd1c6;             /* Paragraph */
  --accent: #f9bc60;            /* Button / highlight (10%) */
  --accent-foreground: #001e1d; /* Button text */
  --destructive: #e16162;       /* Tertiary / error */
  --stroke: #001e1d;            /* Strokes / borders */
  --surface: #e8e4e6;           /* Illustration main (use sparingly) */

  /* Semantic mappings */
  --primary: var(--accent);
  --primary-foreground: var(--accent-foreground);
  --card: #001e1d;
  --card-foreground: #fffffe;
  --border: var(--stroke);
  --input: #001e1d;
  --ring: var(--accent);
}

/* Typography System - 4 Sizes, 2 Weights */
.text-size-1 { @apply text-3xl font-semibold; }    /* Large headings */
.text-size-2 { @apply text-xl font-semibold; }     /* Subheadings */
.text-size-3 { @apply text-base font-normal; }     /* Body text */
.text-size-4 { @apply text-sm font-normal; }       /* Small text/labels */

/* 8pt Grid System Utilities */
.spacing-1 { @apply p-2; }      /* 8px */
.spacing-2 { @apply p-4; }      /* 16px */
.spacing-3 { @apply p-6; }      /* 24px */
.spacing-4 { @apply p-8; }      /* 32px */
.spacing-5 { @apply p-10; }     /* 40px */
.spacing-6 { @apply p-12; }     /* 48px */

.gap-grid-1 { @apply gap-2; }   /* 8px */
.gap-grid-2 { @apply gap-4; }   /* 16px */
.gap-grid-3 { @apply gap-6; }   /* 24px */
.gap-grid-4 { @apply gap-8; }   /* 32px */

body {
  font-family:
    "Inter Variable",
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
  color: var(--muted);
  background: var(--background);
  line-height: 1.6;
}

/* Base typography colors */
h1, h2, h3, h4, h5, h6 { color: var(--foreground); }
a { color: var(--accent); }
a:hover { text-decoration: underline; }

/* Component Base Styles */
.auth-input-field {
  @apply w-full px-4 py-3 rounded-lg bg-transparent border border-border
         focus:border-accent focus:ring-2 focus:ring-accent/20
         outline-none transition-all duration-200
         shadow-sm hover:shadow-md text-size-3 text-foreground;
}

.auth-button {
  @apply w-full px-6 py-3 rounded-lg font-semibold
         transition-all duration-200 shadow-sm hover:shadow-md
         disabled:opacity-50 disabled:cursor-not-allowed text-size-3;
  background-color: var(--accent);
  color: var(--accent-foreground);
}

.auth-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(249, 188, 96, 0.3);
}

/* Card Components */
.card {
  @apply bg-card rounded-xl border border-border shadow-sm hover:shadow-md transition-shadow duration-200;
}

.card-header {
  @apply p-6 border-b border-border;
}

.card-content {
  @apply p-6;
}

/* Button Variants */
.btn-primary {
  @apply px-6 py-3 rounded-lg font-semibold transition-all duration-200 text-size-3;
  background-color: var(--accent);
  color: var(--accent-foreground);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(249, 188, 96, 0.3);
}

.btn-secondary {
  @apply px-6 py-3 rounded-lg font-semibold border-2 transition-all duration-200 text-size-3;
  border-color: var(--accent);
  color: var(--accent);
  background: transparent;
}

.btn-secondary:hover {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

/* Navigation Styles */
.nav-tab {
  @apply px-4 py-3 rounded-lg font-medium transition-all duration-200 text-size-3;
}

.nav-tab.active {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.nav-tab:not(.active) {
  color: var(--muted);
}

.nav-tab:not(.active):hover {
  background-color: var(--stroke);
  color: var(--foreground);
}
