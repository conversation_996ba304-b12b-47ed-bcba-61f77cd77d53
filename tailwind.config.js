const { fontFamily } = require("tailwindcss/defaultTheme");

module.exports = {
  mode: "jit",
  darkMode: "class",
  purge: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Inter var", ...fontFamily.sans],
      },
      // 8pt Grid System - All values divisible by 8 or 4
      borderRadius: {
        DEFAULT: "8px",
        sm: "4px",
        lg: "12px",
        xl: "16px",
        "2xl": "24px",
      },
      boxShadow: {
        DEFAULT: "0 1px 4px rgba(0, 0, 0, 0.1)",
        md: "0 4px 12px rgba(0, 0, 0, 0.1)",
        lg: "0 8px 24px rgba(0, 0, 0, 0.12)",
        hover: "0 2px 8px rgba(0, 0, 0, 0.12)",
      },
      // Palette from docs/PALETTE.md (dark mode only)
      colors: {
        background: "#004643",
        foreground: "#fffffe", // Headline
        paragraph: "#abd1c6",
        accent: "#f9bc60",
        "accent-foreground": "#001e1d",
        stroke: "#001e1d",
        surface: "#e8e4e6",
        secondary: "#abd1c6",
        tertiary: "#e16162",

        // Back-compat aliases used across components
        primary: "#f9bc60",
        "primary-hover": "#f9bc60",
        "primary-foreground": "#001e1d",
        card: "#001e1d",
        "card-foreground": "#fffffe",
        border: "#001e1d",
        muted: "#abd1c6",
      },
      // 8pt Grid Spacing System
      spacing: {
        "grid-1": "4px",    // 4px
        "grid-2": "8px",    // 8px
        "grid-3": "12px",   // 12px
        "grid-4": "16px",   // 16px
        "grid-5": "20px",   // 20px
        "grid-6": "24px",   // 24px
        "grid-8": "32px",   // 32px
        "grid-10": "40px",  // 40px
        "grid-12": "48px",  // 48px
        "grid-16": "64px",  // 64px
        "grid-20": "80px",  // 80px
        "grid-24": "96px",  // 96px
      },
      // Typography Scale - 4 Sizes Only
      fontSize: {
        'size-1': ['2rem', { lineHeight: '2.5rem', fontWeight: '600' }],      // Large headings
        'size-2': ['1.25rem', { lineHeight: '1.75rem', fontWeight: '600' }],  // Subheadings
        'size-3': ['1rem', { lineHeight: '1.5rem', fontWeight: '400' }],      // Body text
        'size-4': ['0.875rem', { lineHeight: '1.25rem', fontWeight: '400' }], // Small text
      },
      // Animation and transitions
      transitionDuration: {
        DEFAULT: '200ms',
      },
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-up': 'slideUp 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  variants: {
    extend: {
      boxShadow: ["hover", "active"],
      transform: ["hover", "active"],
    },
  },
};
