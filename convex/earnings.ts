import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

export const overview = query({
  args: {
    partnerId: v.optional(v.id("users")), // Allow viewing specific partner's earnings
  },
  handler: async (ctx, { partnerId }) => {
    const me = await requireUser(ctx, "any");
    
    // Determine which partner's earnings to show
    let targetPartnerId = partnerId || me._id;
    
    // If user is not a partner and no specific partnerId provided, they can't view earnings
    if (me.role !== "partner" && !partnerId) {
      return {
        estimated: 0,
        withdrawn: 0,
        pending: 0,
        withdrawals: [],
      };
    }
    
    // Permission check: partners can only see their own, admins can see any
    if (me.role === "partner" && targetPartnerId !== me._id) {
      throw new Error("Access denied");
    }
    
    if (!me.role || !["partner", "admin", "superadmin", "accounting"].includes(me.role)) {
      throw new Error("Access denied");
    }
    
    const deals = await ctx.db
      .query("deals")
      .withIndex("by_partner_status", (q) => q.eq("partnerId", targetPartnerId))
      .collect();
    
    // Calculate earnings based on deal status
    let estimated = 0;
    let pending = 0;
    
    for (const deal of deals) {
      const totalCommission = (deal.commissionDueTokenUsd || 0) + (deal.commissionDueFiatUsd || 0) + (deal.commissionPendingUsd || 0);
      
      if (deal.status === "closed" || deal.status === "paid") {
        // Closed and paid deals contribute to pending earnings
        pending += totalCommission;
      } else if (deal.status === "in_progress") {
        // In-progress deals are estimated earnings
        estimated += totalCommission;
      }
      // Lost deals contribute nothing
    }
    
    const withdrawals = await ctx.db
      .query("withdrawals")
      .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
      .collect();
    
    const withdrawn = withdrawals
      .filter(w => w.status === "paid")
      .reduce((sum, w) => sum + w.amountUsd, 0);
    
    // Subtract already withdrawn amounts from pending
    const availablePending = Math.max(0, pending - withdrawn);
    
    return { 
      estimated, 
      withdrawn, 
      pending: availablePending,
      totalCommissionEarned: pending, // Total earned before withdrawals
      withdrawals 
    };
  },
});

export const requestWithdrawal = mutation({
  args: {
    amountUsd: v.number(),
    method: v.union(v.literal("usdt"), v.literal("bank")),
    walletAddress: v.optional(v.string()),
    bankDetails: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const me = await requireUser(ctx, ["partner"]);
    
    // Minimum withdrawal check
    if (args.amountUsd < 100) {
      throw new Error("Minimum withdrawal amount is $100");
    }
    
    // Validate required fields based on method
    if (args.method === "usdt" && !args.walletAddress) {
      throw new Error("Wallet address required for USDT withdrawals");
    }
    
    if (args.method === "bank" && !args.bankDetails) {
      throw new Error("Bank details required for bank withdrawals");
    }
    
    const withdrawalId = await ctx.db.insert("withdrawals", {
      partnerId: me._id,
      amountUsd: args.amountUsd,
      method: args.method,
      walletAddress: args.walletAddress,
      bankDetails: args.bankDetails,
      status: "in_review",
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: me._id,
      action: "WITHDRAWAL_REQUESTED",
      entity: `withdrawals/${withdrawalId}`,
      meta: { amountUsd: args.amountUsd, method: args.method },
      at: Date.now(),
    });
    
    return withdrawalId;
  },
});

export const updateWithdrawalStatus = mutation({
  args: {
    withdrawalId: v.id("withdrawals"),
    status: v.union(v.literal("in_review"), v.literal("approved"), v.literal("paid"), v.literal("rejected")),
    txIdOrRef: v.optional(v.string()),
  },
  handler: async (ctx, { withdrawalId, status, txIdOrRef }) => {
    const user = await requireUser(ctx, ["accounting", "admin", "superadmin"]);
    
    await ctx.db.patch(withdrawalId, { 
      status,
      txIdOrRef,
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: "WITHDRAWAL_STATUS_UPDATED",
      entity: `withdrawals/${withdrawalId}`,
      meta: { status, txIdOrRef },
      at: Date.now(),
    });
    
    return { ok: true };
  },
});

export const listWithdrawals = query({
  args: { 
    partnerId: v.optional(v.id("users")),
    status: v.optional(v.string()),
  },
  handler: async (ctx, { partnerId, status }) => {
    const viewer = await requireUser(ctx, "any");
    
    let targetPartnerId = partnerId;
    if (viewer.role === "partner") {
      targetPartnerId = viewer._id;
    }
    
    if (!targetPartnerId) {
      // Internal users see all withdrawals
      const withdrawals = await ctx.db.query("withdrawals").collect();
      return status ? withdrawals.filter(w => w.status === status) : withdrawals;
    }
    
    const withdrawals = await ctx.db
      .query("withdrawals")
      .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
      .collect();
      
    return status ? withdrawals.filter(w => w.status === status) : withdrawals;
  }
});
